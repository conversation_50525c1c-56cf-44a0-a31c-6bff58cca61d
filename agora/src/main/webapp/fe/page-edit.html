-{% extends "fe/include/base.html" %}

{% block title %}{{ label('page.edit.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}

<meta name="description" content="{{ metaDescription }}">
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:url"                content="{{ publicUrl }}" />
<meta property="og:type"               content="website" />
<meta property="og:title"              content="{{ metaTitle }}" />
<meta property="og:description"        content="{{ metaDescription }}" />
<meta property="og:image"              content="" />
<meta property="og:image:width"        content="1200" />
<meta property="og:image:height"       content="630" />
<meta property="og:image:alt"          content="{{ metaTitle }}" />
{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/css/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/glightbox-master/dist/css/glightbox.min.css">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/description-truncation.css?{{ buildNumber }}">
{% endblock %}

{% block content %}
<a id="pageEditSaveUri" style="display: none" href="{{ paths('PAGE_EDIT_SAVE') }}?oid={{ page.id }}" rel="nofollow"></a>
<a id="pageEditUri" style="display: none" href="{{ paths('PAGE_EDIT') }}?oid={{ page.id }}" rel="nofollow"></a>
<a id="accountInfoUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>
<a id="backUri" style="display: none" href="{{ backUrl }}" rel="nofollow"></a>
<a id="dataPageTagUri" style="display: none" href="{{ paths('DATA_TAG_PAGE') }}"></a>
<a id="dataPageUserUri" style="display: none" href="{{ paths('DATA_USER') }}"></a>
<input type="hidden" id="language" value="{{ language }}">

<div id="common.truncation.preview" style="display: none">{{ label('common.truncation.preview') | raw }}</div>

<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">
            <!-- Account settings START -->
            <div class="card mb-4">

                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('page.edit') | raw }}</h1>
                    <p class="mb-0">{{ label('page.edit.public.and.visible') | raw }}</p>
                </div>
                <!-- Card header END -->

                <!-- Card body START -->
                <div class="card-body">
                    <form id="form-page-edit" method="post">

                        <div class="row g-3 mb-3">
                            <!-- Page information -->
                            <div class="col-12">
                                <label class="form-label">{{ label('page.edit.name') | raw }}</label>
                                <input type="text" class="form-control camel-case" placeholder="{{ label('page.edit.name') | raw }}" id="name" name="name" value="{{ page.name }}" required>
                            </div>                                
                        </div>                                                                                
                        <div class="row g-3 mb-3">
                            <!-- Profile phone -->
                            <div class="col-lg-3">
                                <label class="form-label">{{ label('common.profile.photo') | raw }}</label>
                                {% set originalFilename = '' %}
                                {% if page.profileImageId is not empty %}
                                {% set fileDecoded = get('DocumentDescriptor', page.profileImageId) %}
                                {% set originalFilename = fileDecoded.metadata.originalFilename %}
                                {% endif %}
                                <div class="slim rounded"
                                     data-max-file-size="5"
                                     data-save-initial-image="{{ page.profileImageId is not empty ? 'true' : 'false'}}"
                                     data-push="false"
                                     data-post="output"
                                     data-label="{{ label('slim.upload.photo') | raw }}"
                                     data-label-loading=" "
                                     data-ratio="1:1"
                                     data-jpeg-compression=100
                                     data-button-edit-label="{{ label('common.edit') | raw }}"
                                     data-button-remove-label="{{ label('common.remove') | raw }}"
                                     data-button-download-label="{{ label('common.download') | raw }}"
                                     data-button-upload-label="{{ label('common.upload') | raw }}"
                                     data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                     data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                     data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                     data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                     data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                     data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                     data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                     data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                     data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                     data-status-upload-success="{{ label('slim.image.saved') | raw }}"
                                     data-meta-originalFilename="{{ originalFilename }}">
                                    {% if page.profileImageId is not empty %}
                                    <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ page.profileImageId }}" alt=""/>
                                    {% endif %}
                                    <input type="file" id="cropper" name="uploaded-profile" data-show-caption="false" data-show-remove="true">
                                </div>
                                <small class="form-text">{{ label('slim.recommended.dimension') | raw }} 600x600 (ratio 1:1)</small>
                            </div>
                            <!-- Cover image -->
                            <div class="col-lg-12">
                                <label class="form-label">{{ label('common.cover.photo') | raw }}</label>
                                {% set originalFilename = '' %}
                                {% if page.profileImageId is not empty %}
                                {% set fileDecoded = get('DocumentDescriptor', page.coverImageId) %}
                                {% set originalFilename = fileDecoded.metadata.originalFilename %}
                                {% endif %}
                                <div class="slim rounded"
                                     data-max-file-size="5"
                                     data-save-initial-image="{{ page.coverImageId is not empty ? 'true' : 'false'}}"
                                     data-push="false"
                                     data-post="output"
                                     data-label="{{ label('slim.upload.photo') | raw }}"
                                     data-label-loading=" "
                                     data-ratio="free"
                                     data-jpeg-compression=100
                                     data-button-edit-label="{{ label('common.edit') | raw }}"
                                     data-button-remove-label="{{ label('common.remove') | raw }}"
                                     data-button-download-label="{{ label('common.download') | raw }}"
                                     data-button-upload-label="{{ label('common.upload') | raw }}"
                                     data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                     data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                     data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                     data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                     data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                     data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                     data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                     data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                     data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                     data-status-upload-success="{{ label('slim.image.saved') | raw }}"
                                     data-meta-originalFilename="{{ originalFilename }}">
                                    {% if page.coverImageId is not empty %}
                                    <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ page.coverImageId }}" alt=""/>
                                    {% endif %}
                                    <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                                </div>
                                <small class="form-text">{{ label('slim.recommended.dimension') | raw }} 1116x280 (ratio 4:1)</small>
                            </div>
                            <!-- End Media -->
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-sm-6">
                                <label class="form-label">{{ label('page.edit.identifier') | raw }}</label>
                                <input type="text" class="form-control" placeholder="{{ label('page.edit.identifier') | raw }}" id="identifier" name="identifier" value="{{ page.identifier }}" required>
                            </div>
                            <!-- Type -->
                            <div class="col-sm-6">
                                <label class="form-label">{{ label('page.type') | raw }}</label>
                                <select class="form-select js-choice pageType" id="pageType" name="pageType" value="{{ page.pageType }}" required {{ page.isUserPage ? 'disabled' : '' }}>
                                    {% for pageType in lookup('area') %}
                                    <option value="{{ pageType.code }}" {{ page.pageType == pageType.code ? 'selected' : '' }}>{{ language == 'it' ? pageType.description : pageType.descriptionEnglish }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <!-- Category -->
                            {#
                            {% if pageType is empty %}
                            {% set pageType = page.pageType %}
                            {% endif %}
                            <div class="col-sm-6" id="categoryPage">
                                {% set categoryDisabled = '' %}
                                {% if pageType is empty %}
                                {% set categoryDisabled = 'disabled' %}
                                {% endif %}
                                {% set categoryList = lookup('category', pageType) %}
                                {% if categoryList is empty %}
                                {% set categoryDisabled = 'disabled' %}
                                {% endif %}
                                <label class="form-label">{{ label('page.category') | raw }}</label>
                                <select class="form-select js-choice" data-search-enabled="true" id="category" name="category" {{ categoryDisabled }}>
                                    {% if categoryDisabled %}
                                    <option value="">-</option>
                                    {% endif %}
                                    {% for category in categoryList %}
                                    <option value="{{ category.code }}" {{ page.category == category.code ? 'selected' : '' }}>{{ language == 'it' ? category.description : category.descriptionEnglish }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            #}
                            <div class="row g-3 mb-3">
                                <div class="col-12">
                                    <label class="form-label">{{ label('common.tags') | raw }}</label>
                                    <select class="select-search-multiple form-control" id="tags" name="tags" data-placeholder="{{ label('tags.placeholder.multiple') | raw }}" multiple>
                                        {% for tag in page.tags %}
                                        <option value="{{ tag | lower }}" selected>{{ tag | lower }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div> 
                        </div>
                        <div class="row g-3 mb-3">
                            <!-- Page information -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.description') | raw }}</label>
                                <textarea class="form-control summernote" rows="3" placeholder="{{ label('common.description') | raw }}" id="description" name="description">{{ page.description }}</textarea>

                                <!-- Truncation Preview -->
                                <div id="description-preview" class="mt-2" style="display: none;">
                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-eye me-1"></i> {{ label('common.truncation.preview') | raw }} ({{ firm.descriptionTruncateLength | default(300) }} {{ label('common.characters') | raw }} ):</h6>
                                        <div id="preview-content" class="border rounded p-2 bg-light"></div>
                                        <small class="mt-1 d-block">
                                            <span id="preview-char-count">0</span> {{ label('common.characters.visible') | raw }}.
                                            <span id="preview-truncated-info" style="display: none;">
                                                {{ label('common.truncation.info') | raw }}
                                            </span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <!-- Page information -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.short.description') | raw }}</label>
                                <textarea class="form-control" rows="2" maxlength="50" placeholder="{{ label('common.short.description') | raw }}" id="shortDescription" name="shortDescription">{{ page.shortDescription }}</textarea>
                            </div>
                            {# se è un luogo prevedere gestione visibilità in base alla selezione del tipo #}
                            <!-- Address -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.fulladdress') | raw }}</label>
                                <input type="text" class="form-control" placeholder="{{ label('common.fulladdress') | raw }}" id="fulladdress" name="fulladdress" value="{{ page.fulladdress }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.country') | raw }}</label>
                                <select class="form-control select-search" name="countryCode" id="countryCode">
                                    <option value="">-</option>
                                    {% for item in lookup("country") %}
                                    <option value="{{ item.code }}" {{ item.code == page.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                    {% endfor %}
                                </select>                                    
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.address') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.address') | raw }}" value="{{ page.address }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.city') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.city') | raw }}" value="{{ page.city }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.postalcode') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="{{ label('common.postalcode') | raw }}" value="{{ page.postalCode }}">
                            </div>
                            <div class="col-12 col-sm-6" id="provinceDiv">
                                <label class="form-label">{{ label('common.province') | raw }}</label>                                    
                                <select class="form-control select-search provinceCode provinceCodeIt" name="provinceCode" id="provinceCode" value="{{ page.provinceCode }}">
                                    <option value="">-</option>
                                    {% for item in lookup("province") %}
                                    <option value="{{ item.code }}" {{ item.code == page.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-12 col-sm-6" id="provinceExtDiv">
                                <label class="form-label">{{ label('common.province') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="provinceCode" id="provinceCode" class="form-control maxlength provinceCode provinceCodeExt" placeholder="{{ label('common.province') | raw }}" value="{{ page.provinceCode }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.extra.city') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.extra.city') | raw }}" value="{{ page.extraAddress }}">
                            </div>
                            <!-- Hide Followers -->
                            <div class="col-md-4">
                                <label class="form-label">{{ label('page.show.follower') | raw }}</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers" {{ page.showFollowers == true ? 'checked' : '' }}>
                                </div>
                            </div>
                            <!-- Divider -->
                            <hr>

                            <!-- Social Links START -->
                            <div class="col-12">
                                <h5 class="card-title mb-0">Links</h5>
                            </div>
                            <!-- Website -->
                            <div class="col-sm-6">
                                <label  class="form-label">{{ label('common.website') | raw }}</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-browser-chrome text-facebook"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link {{ label('common.website') | raw }}" id="websiteUrl" name="websiteUrl" value="{{ page.websiteUrl }}">
                                </div>
                            </div>
                            <!-- Facebook -->
                            <div class="col-sm-6">
                                <label  class="form-label">Facebook</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-facebook text-facebook"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Facebook" id="facebookUrl" name="facebookUrl" value="{{ page.facebookUrl }}">
                                </div>
                            </div>
                            <!-- Twitter -->
                            <div class="col-sm-6">
                                <label class="form-label">Twitter</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-twitter text-twitter"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Twitter" id="twitterUrl" name="twitterUrl" value="{{ page.twitterUrl }}">
                                </div>
                            </div>
                            <!-- Instagram -->
                            <div class="col-sm-6">
                                <label class="form-label">Instagram</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-instagram text-instagram"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Instagram" id="instagramUrl" name="instagramUrl" value="{{ page.instagramUrl }}">
                                </div>
                            </div>
                            <!-- Linkedin -->
                            <div class="col-sm-6">
                                <label class="form-label">Linkedin</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-linkedin"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Linkedin" id="linkedinUrl" name="linkedinUrl" value="{{ page.linkedinUrl }}">
                                </div>
                            </div>
                            <!-- YouTube -->
                            <div class="col-sm-6">
                                <label class="form-label">YouTube</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-youtube"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link YouTube" id="youtubeUrl" name="youtubeUrl" value="{{ page.youtubeUrl }}">
                                </div>
                            </div>
                            <!-- Reddit -->
                            <div class="col-sm-6">
                                <label class="form-label">Reddit</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-reddit"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Reddit" id="redditUrl" name="redditUrl" value="{{ page.redditUrl }}">
                                </div>
                            </div>
                            <!-- Medium -->
                            <div class="col-sm-6">
                                <label class="form-label">Medium</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-medium"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Medium" id="mediumUrl" name="mediumUrl" value="{{ page.mediumUrl }}">
                                </div>
                            </div>
                            <!-- tiktok -->
                            <div class="col-sm-6">
                                <label class="form-label">TikTok</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-tiktok"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link TikTok" id="tiktok" name="tiktok" value="{{ page.tiktok }}">
                                </div>
                            </div>
                            <!-- Spotify -->
                            <div class="col-sm-6">
                                <label class="form-label">Spotify</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-spotify"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Spotify" id="spotifiyUrl" name="spotifiyUrl" value="{{ page.spotifiyUrl }}">
                                </div>
                            </div>                                
                        </div>
                        <hr>
                        <div class="row g-3 mb-3">
                            <!-- Social Links START -->
                            <div class="col-12">
                                <h5 class="card-title mb-0">{{ label('common.permissions') | raw }}</h5>
                            </div>                                
                            <div class="col-sm-6">
                                <label class="form-label">{{ label('page.who.can.post') | raw }}</label>
                                <select class="form-select js-choice pageType" id="pageTagging" name="pageTagging" required>
                                    <option value="everyone" {{ page.pageTagging == 'everyone' ? 'selected' : '' }}>{{ label('common.everyone') | raw }}</option>                                        
                                    <option value="owner" {{ page.pageTagging == 'owner' ? 'selected' : '' }}>{{ label('common.only.owner') | raw }}</option>                                        
                                </select>
                            </div>
                            {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                            <!-- Divider -->
                            <hr>
                            <div class="row g-3 mb-3 mt-1">
                                <!-- Social Links START -->
                                <div class="col-12">
                                    <h5 class="card-title mb-0">INTERNAL STUFF</h5>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Owner ID</label>
                                    <select class="form-select" id="ownerId" name="ownerId" required>
                                        {% if page.ownerId is not empty %}
                                        {% set owner = get('user', page.ownerId) %}
                                        <option value="{{ page.ownerId }}">{{ owner.name }} ({{ owner.email }})</option>
                                        {% endif %}
                                    </select>
                                </div>
                                <div class="col-sm-6">
                                    <label class="form-label">Pagina Rivendicata</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" role="switch" id="claimed" name="claimed" {{ page.claimed == true ? 'checked' : '' }}>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            <!-- Divider -->
                            <hr>
                            <!-- Button  -->
                            <div class="col-12 text-end">
                                <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('page.edit.update') | raw }}</button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}

{% block pagescripts %}
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT"></script>
<script src="{{ contextPath }}/fe/js/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/glightbox-master/dist/js/glightbox.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<script src="{{ contextPath }}/fe/js/content-truncation.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/js/pages/page-edit.js?{{ buildNumber }}"></script>

<script>
$(document).ready(function() {
    var truncateLength = {{ firm.descriptionTruncateLength | default(300) }};
    var $preview = $('#description-preview');
    var $previewContent = $('#preview-content');
    var $charCount = $('#preview-char-count');
    var $truncatedInfo = $('#preview-truncated-info');

    function updatePreview() {
        if (!window.ContentTruncation) {
            return;
        }

        var content = $('#description').summernote('code');
        if (!content || content.trim() === '' || content === '<p><br></p>' || content.trim().length <= truncateLength) {
            $preview.hide();
            return;
        }

        var result = window.ContentTruncation.truncateHtml(content, truncateLength);

        if (result.isTruncated) {
            // Show truncated version with "..." and note about full content
            $previewContent.html(result.truncated + "... <em>[{{ label('common.show.more') | raw }}]</em>");
            $truncatedInfo.show();
        } else {
            // Show complete content as-is
            $previewContent.html(content);
            $truncatedInfo.hide();
        }

        $charCount.text(result.visibleLength);
        $preview.show();
    }

    // Update preview when Summernote content changes
    $('#description').on('summernote.change', function() {
        setTimeout(updatePreview, 100); // Small delay to ensure content is updated
    });

    // Initial preview update
    setTimeout(function() {
        if ($('#description').length && typeof $('#description').summernote === 'function') {
            updatePreview();
        }
    }, 1000); // Wait for Summernote to initialize
});
</script>
{% endblock %}